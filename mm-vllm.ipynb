from __future__ import annotations
from typing import Any, Dict, List, Optional, Tuple
import time
import os
from some import BaseLanguageModel, register_language_model
from some.inference import get_language_model
from some.metrics import LLMMetricsCollector, SchemaMetricsCollector
from some.prompting import BasePromptBuilder
from pydantic import BaseModel



class CustomLanguageModel(BaseLanguageModel):
    """Simple custom language model for testing."""

    def __init__(self, *, model: Optional[str] = None, **kwargs) -> None:
        super().__init__(model=model or "simple-custom")

    def generate(
        self,
        inputs: List[Dict[str, Any]],
        *,
        max_workers: Optional[int] = None,
    ) -> Tuple[List[Dict[str, Any]], int, float]:
        """Generate simple mock responses."""
        start_time = time.time()
        results = []

        for item in inputs:
            response = self._generate_single(item)
            results.append(response)
            # Simulate some processing time
            time.sleep(0.01)

        total_inference_time = time.time() - start_time
        return results, len(inputs), total_inference_time
    
    def _generate_single(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        result_key = payload.get("result_key", "result")
        return {
            "input_tokens": 10,
            "output_tokens": 20,
            result_key: {},
        }

register_language_model("custom", lambda **kw: CustomLanguageModel(**kw))
    


class ProductPrompt(BasePromptBuilder):
    def build(self, item: Dict[str, Any]) -> Dict[str, Any]:
        text = item["text"]
        return {
            "messages": [{"role": "user", "content": f"Extract ProductSpec as JSON from this text and adhere strictly to the schema.\n{text}"}],
            "response_format": ProductSpec,
            "result_key": "product",
        }
    
class ProductSpec(BaseModel):
    name: str
    price: float
    features: List[str]






from openai import OpenAI

class OpenAILanguageModel(BaseLanguageModel):
    def __init__(self, *, api_key: Optional[str] = None, base_url: Optional[str] = None, model: Optional[str] = None):
        super().__init__(model=model)
        key = api_key or os.getenv("OPENAI_API_KEY")
        if not key:
            raise RuntimeError("OPENAI_API_KEY is required for provider 'openai'.")
        # Resolve base URL default here
        resolved_base = base_url or os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1")
        self.client = OpenAI(base_url=resolved_base, api_key=key)
        # Default model if not provided at init
        if self.model_id is None:
            self.model_id = os.getenv("OPENAI_MODEL", get_default_model("openai") or "gpt-5-nano")

    def _generate_single(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        messages: List[Dict[str, str]] = payload.get("messages", [])
        response_format = payload.get("response_format")  # prompt builders should supply when structured output is desired
        result_key = payload.get("result_key", "result")

        completion = self.client.beta.chat.completions.parse(
            model=self.model_id,
            messages=messages,
            response_format=response_format,
        )

        return {
            "input_tokens": completion.usage.prompt_tokens,
            "output_tokens": completion.usage.completion_tokens,
            result_key: completion.choices[0].message.parsed.model_dump(),
        }

    def generate(self, inputs: List[Dict[str, Any]], *, max_workers: Optional[int] = None):
        if not inputs:
            return [], 0, 0.0

        # Reasonable cap to avoid high parallelism on APIs
        if max_workers is None:
            cpu_count = os.cpu_count()
            max_workers = max(1, (cpu_count or 4) - 1)
        max_workers = min(max_workers, len(inputs), 10)

        results: List[Dict[str, Any]] = [{}] * len(inputs)

        def task(idx: int, item: Dict[str, Any]):
            try:
                res = self._generate_single(item)
                results[idx] = res
            except Exception as e:
                logging.error("OpenAI inference error for item %d: %s", idx, e)
                # Use result_key if present to keep downstream consistent
                result_key = item.get("result_key", "result")
                results[idx] = {
                    "input_tokens": 0,
                    "output_tokens": 0,
                    result_key: None,
                    "error": str(e),
                }

        start_time = time.time()
        with ThreadPoolExecutor(max_workers=max_workers) as ex:
            futures = [ex.submit(task, i, item) for i, item in enumerate(inputs)]
            for _ in as_completed_with_tqdm(
                futures,
                total=len(futures),
                desc="LLM",
                unit="item",
                colour="magenta",
            ):
                pass

        total_inference_time = time.time() - start_time

        return results, max_workers, total_inference_time
    
register_language_model("mm", lambda **kw: CustomLanguageModel(**kw))

class ProductPrompt(BasePromptBuilder):
    def build(self, item: Dict[str, Any]) -> Dict[str, Any]:
        text = item["text"]
        return {
            "messages": [{"role": "user", "content": f"Extract ProductSpec as JSON from this text and adhere strictly to the schema.\n{text}"}],
            "response_format": ProductSpec,
            "result_key": "product",
        }
    
    
class DescriptionFace(BaseModel):
    hairColor: str
    eyeColor: str
    facialHair: bool

import base64
from PIL import Image
from io import BytesIO

def encode_base64_content_from_path(content_path: str) -> str:
    """Encode a local image file to base64 format."""
    with Image.open(content_path) as img:
        buffer = BytesIO()
        img.save(buffer, format=img.format)  # preserve original format (JPEG, PNG, etc.)
        return base64.b64encode(buffer.getvalue()).decode("utf-8")


class WhoIsThis(BasePromptBuilder):
    def build(self, item: Dict[str, Any]) -> Dict[str, Any]:
        text = item["text"]
        image_path = item["image_path"]
        base64_image = encode_base64_content_from_path(image_path)
        return {
            "messages": [
                {
                    "role": "user", 
                    "content": 
                    [
                        {
                            "type": "text",
                            "text": f"Extract DescriptionFace as JSON from this image and adhere strictly to the schema from what you see.\n{text}"
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url":  f"data:image/jpg;base64,{base64_image}"
                            },
                        },
                    ]
                }
                 ],
            "response_format": DescriptionFace,
            "result_key": "description",
        }
    

    

    



!pip install anthropic



base64_image = encode_base64_content_from_path("rdj.jpg")

messages = [
        {
            "role": "user",
            "content": [
                {
                    "type": "image",
                    "source": {
                        "type": "base64",
                        "media_type": "image/jpeg",
                        "data": base64_image,
                    },
                },
                {
                    "type": "text",
                    "text": "Describe this image."
                }
            ],
        }
    ]

class WhoIsThis(BasePromptBuilder):
    def build(self, item: Dict[str, Any]) -> Dict[str, Any]:
        text = item["text"]
        image_path = item["image_path"]
        base64_image = encode_base64_content_from_path(image_path)
        return {
            "messages": [
                {
                    "role": "user", 
                    "content": 
                    [
                        {
                            "type": "text",
                            "text": f"Extract DescriptionFace as JSON from this image and adhere strictly to the schema from what you see.\n{text}"
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url":  f"data:image/jpg;base64,{base64_image}"
                            },
                        },
                    ]
                }
                 ],
            "response_format": DescriptionFace,
            "result_key": "description",
        }
    

    


import anthropic, dotenv
dotenv.load_dotenv()



client = anthropic.Anthropic()
message = client.messages.create(
    model="claude-sonnet-4-20250514",
    max_tokens=1024,
    messages=messages,
)
print(message)





items = [
    {"text": "", "image_path": "rdj.jpg"},
]

# Build inputs using the prompt builder
inputs = [WhoIsThis().build(x) for x in items]

# Get language model
provider = "ollama"  # Change to "custom" if you want to test the mock model
# model = "gpt-5-nano"
model = "gemma3:4b"

lm = get_language_model(provider=provider)#, model=model)
print(lm.generate(inputs))

inputs





items = [
    {"text": "Widget X costs $19.99 and includes wifi, gps."},
    {"text": "Gadget Y is priced at $49.50, features: bluetooth, waterproofing"},
    {"text": "Device Z sells for $129.00 with premium materials and AI processing"},
]

# Build inputs using the prompt builder
inputs = [ProductPrompt().build(x) for x in items]

# Get language model
provider = "mm"  # Change to "custom" if you want to test the mock model
# model = "gpt-5-nano"
lm = get_language_model(provider=provider)#, model=model)
print(lm.generate(inputs))